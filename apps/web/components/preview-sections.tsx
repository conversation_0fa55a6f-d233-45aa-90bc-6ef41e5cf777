"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { BookOpen, ShoppingBag, ChefHat, Mic, Users, User, ArrowRight, Star, Clock, Heart } from "lucide-react"
import { useTranslations } from "next-intl"
import { useParams } from "next/navigation"

export function PreviewSections() {
  const t = useTranslations("sections")
  const tCommon = useTranslations("common")
  const params = useParams()
  const locale = params.locale as string

  const sections = [
    {
      id: "blog",
      title: t("blog.title"),
      description: t("blog.description"),
      icon: BookOpen,
      color: "bg-primary",
      posts: [
        { title: "The Art of Mindful Living", date: "2 days ago", readTime: "5 min read" },
        { title: "Creative Inspiration Daily", date: "1 week ago", readTime: "3 min read" },
        { title: "Building Better Habits", date: "2 weeks ago", readTime: "7 min read" },
      ],
    },
    {
      id: "store",
      title: t("store.title"),
      description: t("store.description"),
      icon: ShoppingBag,
      color: "bg-accent",
      products: [
        { name: "Minimalist Notebook", price: "$24.99", rating: 4.8 },
        { name: "Ceramic Coffee Mug", price: "$18.99", rating: 4.9 },
        { name: "Desk Plant Collection", price: "$45.99", rating: 4.7 },
      ],
    },
    {
      id: "recipes",
      title: t("recipes.title"),
      description: t("recipes.description"),
      icon: ChefHat,
      color: "bg-secondary",
      recipes: [
        { name: "Mediterranean Bowl", time: "25 min", difficulty: "Easy" },
        { name: "Green Smoothie Power", time: "5 min", difficulty: "Easy" },
        { name: "Homemade Pasta", time: "45 min", difficulty: "Medium" },
      ],
    },
    {
      id: "podcast",
      title: t("podcast.title"),
      description: t("podcast.description"),
      icon: Mic,
      color: "bg-primary",
      episodes: [
        { title: "Finding Your Creative Voice", duration: "42 min", guests: "Sarah Chen" },
        { title: "The Future of Design", duration: "38 min", guests: "Alex Rivera" },
        { title: "Mindfulness in Work", duration: "35 min", guests: "Dr. Maya Patel" },
      ],
    },
    {
      id: "community",
      title: t("ugc.title"),
      description: t("ugc.description"),
      icon: Users,
      color: "bg-accent",
      stats: [
        { label: "Active Members", value: "12.5K" },
        { label: "Posts This Week", value: "847" },
        { label: "Countries", value: "45" },
      ],
    },
    {
      id: "about",
      title: t("about.title"),
      description: t("about.description"),
      icon: User,
      color: "bg-secondary",
      highlights: ["Creative entrepreneur", "Wellness advocate", "Community builder"],
    },
  ]

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-4">Explore Everything</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            From inspiring content to unique products, discover all the ways we can help you live more creatively and
            mindfully.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {sections.map((section) => {
            const IconComponent = section.icon
            return (
              <Card
                key={section.id}
                className="group hover:shadow-soft-hover transition-all duration-300 border-0 shadow-soft rounded-2xl overflow-hidden"
              >
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`${section.color} p-2 rounded-xl`}>
                      <IconComponent className="h-5 w-5 text-white" />
                    </div>
                    <CardTitle className="text-xl font-semibold">{section.title}</CardTitle>
                  </div>
                  <CardDescription className="text-muted-foreground">{section.description}</CardDescription>
                </CardHeader>

                <CardContent className="pt-0">
                  {/* Blog Preview */}
                  {section.id === "blog" && (
                    <div className="space-y-3">
                      {section.posts?.map((post, index) => (
                        <div
                          key={index}
                          className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors"
                        >
                          <div className="flex-1">
                            <h4 className="font-medium text-sm mb-1">{post.title}</h4>
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <Clock className="h-3 w-3" />
                              <span>{post.readTime}</span>
                              <span>•</span>
                              <span>{post.date}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Store Preview */}
                  {section.id === "store" && (
                    <div className="space-y-3">
                      {section.products?.map((product, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors"
                        >
                          <div>
                            <h4 className="font-medium text-sm">{product.name}</h4>
                            <div className="flex items-center gap-1 mt-1">
                              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                              <span className="text-xs text-muted-foreground">{product.rating}</span>
                            </div>
                          </div>
                          <span className="font-semibold text-primary">{product.price}</span>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Recipes Preview */}
                  {section.id === "recipes" && (
                    <div className="space-y-3">
                      {section.recipes?.map((recipe, index) => (
                        <div key={index} className="p-3 rounded-lg hover:bg-muted/50 transition-colors">
                          <h4 className="font-medium text-sm mb-2">{recipe.name}</h4>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{recipe.time}</span>
                            </div>
                            <Badge variant="secondary" className="text-xs">
                              {recipe.difficulty}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Podcast Preview */}
                  {section.id === "podcast" && (
                    <div className="space-y-3">
                      {section.episodes?.map((episode, index) => (
                        <div key={index} className="p-3 rounded-lg hover:bg-muted/50 transition-colors">
                          <h4 className="font-medium text-sm mb-1">{episode.title}</h4>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>{episode.duration}</span>
                            <span>•</span>
                            <span>with {episode.guests}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Community Preview */}
                  {section.id === "community" && (
                    <div className="grid grid-cols-3 gap-4">
                      {section.stats?.map((stat, index) => (
                        <div key={index} className="text-center">
                          <div className="font-bold text-lg text-primary">{stat.value}</div>
                          <div className="text-xs text-muted-foreground">{stat.label}</div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* About Preview */}
                  {section.id === "about" && (
                    <div className="space-y-2">
                      {section.highlights?.map((highlight, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <Heart className="h-3 w-3 text-accent" />
                          <span className="text-sm">{highlight}</span>
                        </div>
                      ))}
                    </div>
                  )}

                  <Button
                    variant="ghost"
                    className="w-full mt-4 group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-300"
                  >
                    Explore {section.title}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </section>
  )
}
