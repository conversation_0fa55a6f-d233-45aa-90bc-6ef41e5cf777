"use client"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Globe, Check } from "lucide-react"
import { useRouter, usePathname } from "next/navigation"
import { useParams } from "next/navigation"

type Language = {
  code: string
  name: string
  flag: string
}

const languages: Language[] = [
  { code: "en", name: "English", flag: "🇺🇸" },
  { code: "et", name: "<PERSON>est<PERSON>", flag: "🇪🇪" },
]

export function LanguageSwitcher() {
  const router = useRouter()
  const pathname = usePathname()
  const params = useParams()
  const currentLanguage = params.locale as string

  const handleLanguageChange = (languageCode: string) => {
    const segments = pathname.split("/").filter(Boolean)
    const pathWithoutLocale = segments.slice(1).join("/")
    const newPath = `/${languageCode}${pathWithoutLocale ? `/${pathWithoutLocale}` : ""}`
    router.push(newPath)
  }

  const currentLang = languages.find((lang) => lang.code === currentLanguage)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-9 px-3 text-foreground/80 hover:text-primary hover:bg-primary/10 rounded-xl transition-all duration-200"
        >
          <Globe className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">{currentLang?.name}</span>
          <span className="sm:hidden">{currentLang?.flag}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-40 rounded-xl border-0 shadow-soft bg-background/95 backdrop-blur-md"
      >
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className="flex items-center justify-between px-3 py-2 cursor-pointer hover:bg-primary/10 rounded-lg transition-colors duration-200"
          >
            <div className="flex items-center gap-2">
              <span>{language.flag}</span>
              <span className="text-sm font-medium">{language.name}</span>
            </div>
            {currentLanguage === language.code && <Check className="h-4 w-4 text-primary" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
