"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Menu, X, Clock, ChevronDown, BookOpen, ChefHat, Mic, ShoppingBag, Camera, Users, User } from "lucide-react"
import { LanguageSwitcher } from "./language-switcher"
import { useTranslations } from "next-intl"
import { useParams } from "next/navigation"

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)

  const t = useTranslations("navigation")
  const params = useParams()
  const locale = (params.locale as string) || 'et' // Default to Estonian for root routes

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Helper function to generate correct href based on locale
  const getHref = (path: string) => {
    return locale === 'et' ? path : `/${locale}${path}`
  }

  const navItems = [
    {
      name: t("content"),
      icon: BookOpen,
      items: [
        { name: t("blog"), href: getHref("/blog"), icon: BookOpen },
        { name: t("recipes"), href: getHref("/recipes"), icon: ChefHat },
        { name: t("podcast"), href: getHref("/podcast"), icon: Mic },
      ],
    },
    { name: t("store"), href: getHref("/store"), icon: ShoppingBag },
    { name: t("ugc"), href: getHref("/ugc"), icon: Camera },
    { name: t("community"), href: getHref("/community"), icon: Users },
    { name: t("about"), href: getHref("/about"), icon: User },
  ]

  const handleDropdownToggle = (itemName: string) => {
    setActiveDropdown(activeDropdown === itemName ? null : itemName)
  }

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? "bg-background/95 backdrop-blur-md shadow-soft" : "bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          <Link href={locale === 'et' ? "/" : `/${locale}`} className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary rounded-xl flex items-center justify-center shadow-soft">
              <Clock className="w-5 h-5 text-primary-foreground" />
            </div>
            <div className="flex flex-col">
              <span className="font-heading font-bold text-xl text-foreground leading-none">Time With Tuuli</span>
              <span className="text-xs text-muted-foreground font-medium">Creative Living</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navItems.map((item) => {
              const ItemIcon = item.icon
              return (
                <div key={item.name} className="relative">
                  {item.items ? (
                    <div className="relative">
                      <button
                        className="flex items-center space-x-2 text-foreground/80 hover:text-primary transition-colors duration-200 font-medium"
                        onClick={() => handleDropdownToggle(item.name)}
                        onMouseEnter={() => setActiveDropdown(item.name)}
                      >
                        <ItemIcon className="w-4 h-4" />
                        <span>{item.name}</span>
                        <ChevronDown className="w-4 h-4" />
                      </button>
                      {activeDropdown === item.name && (
                        <div
                          className="absolute top-full left-0 mt-2 bg-background/95 backdrop-blur-md rounded-xl shadow-soft border border-border min-w-[160px] py-2"
                          onMouseLeave={() => setActiveDropdown(null)}
                        >
                          {item.items.map((subItem) => {
                            const SubIcon = subItem.icon
                            return (
                              <Link
                                key={subItem.name}
                                href={subItem.href}
                                className="flex items-center space-x-2 px-4 py-2 text-foreground/80 hover:text-primary hover:bg-secondary/50 transition-colors duration-200"
                                onClick={() => setActiveDropdown(null)}
                              >
                                <SubIcon className="w-4 h-4" />
                                <span>{subItem.name}</span>
                              </Link>
                            )
                          })}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className="flex items-center space-x-2 text-foreground/80 hover:text-primary transition-colors duration-200 font-medium"
                    >
                      <ItemIcon className="w-4 h-4" />
                      <span>{item.name}</span>
                    </Link>
                  )}
                </div>
              )
            })}
          </nav>

          <div className="hidden lg:flex items-center space-x-4">
            <LanguageSwitcher />
            <Button className="bg-accent hover:bg-accent/90 text-accent-foreground rounded-xl px-6 shadow-soft hover:shadow-soft-hover transition-all duration-200">
              {t("joinCommunity")}
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <div className="flex items-center space-x-2 lg:hidden">
            <LanguageSwitcher />
            <button className="p-2 text-foreground" onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}>
              {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden absolute top-full left-0 right-0 bg-background/95 backdrop-blur-md border-t border-border shadow-soft">
            <nav className="flex flex-col space-y-2 p-4">
              {navItems.map((item) => {
                const ItemIcon = item.icon
                return (
                  <div key={item.name}>
                    {item.items ? (
                      <div>
                        <button
                          className="flex items-center justify-between w-full text-foreground/80 hover:text-primary transition-colors duration-200 font-medium py-2"
                          onClick={() => handleDropdownToggle(item.name)}
                        >
                          <div className="flex items-center space-x-2">
                            <ItemIcon className="w-4 h-4" />
                            <span>{item.name}</span>
                          </div>
                          <ChevronDown
                            className={`w-4 h-4 transition-transform ${activeDropdown === item.name ? "rotate-180" : ""}`}
                          />
                        </button>
                        {activeDropdown === item.name && (
                          <div className="ml-6 space-y-2 mt-2">
                            {item.items.map((subItem) => {
                              const SubIcon = subItem.icon
                              return (
                                <Link
                                  key={subItem.name}
                                  href={subItem.href}
                                  className="flex items-center space-x-2 text-foreground/70 hover:text-primary transition-colors duration-200 py-1"
                                  onClick={() => setIsMobileMenuOpen(false)}
                                >
                                  <SubIcon className="w-4 h-4" />
                                  <span>{subItem.name}</span>
                                </Link>
                              )
                            })}
                          </div>
                        )}
                      </div>
                    ) : (
                      <Link
                        href={item.href}
                        className="flex items-center space-x-2 text-foreground/80 hover:text-primary transition-colors duration-200 font-medium py-2"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        <ItemIcon className="w-4 h-4" />
                        <span>{item.name}</span>
                      </Link>
                    )}
                  </div>
                )
              })}
              <Button className="bg-accent hover:bg-accent/90 text-accent-foreground rounded-xl mt-4">
                {t("joinCommunity")}
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
