"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Share2, Facebook, Twitter, Linkedin, Link, Check } from "lucide-react"

interface SocialShareProps {
  title: string
  url?: string
  description?: string
  className?: string
}

export function SocialShare({ title, url, description, className = "" }: SocialShareProps) {
  const [copied, setCopied] = useState(false)
  const [isOpen, setIsOpen] = useState(false)

  const shareUrl = url || (typeof window !== "undefined" ? window.location.href : "")
  const shareTitle = encodeURIComponent(title)
  const shareDescription = encodeURIComponent(description || "")

  const shareLinks = {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
    twitter: `https://twitter.com/intent/tweet?text=${shareTitle}&url=${encodeURIComponent(shareUrl)}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`,
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error("Failed to copy: ", err)
    }
  }

  const openShareWindow = (url: string) => {
    window.open(url, "_blank", "width=600,height=400,scrollbars=yes,resizable=yes")
  }

  return (
    <div className={`relative ${className}`}>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="rounded-full border-warm-sand hover:bg-muted-teal hover:text-white transition-colors"
      >
        <Share2 className="h-4 w-4 mr-2" />
        Share
      </Button>

      {isOpen && (
        <>
          <div className="fixed inset-0 z-10" onClick={() => setIsOpen(false)} />
          <div className="absolute top-full mt-2 right-0 bg-white rounded-xl shadow-soft border border-warm-sand/20 p-4 z-20 min-w-48">
            <div className="space-y-2">
              <button
                onClick={() => openShareWindow(shareLinks.facebook)}
                className="w-full flex items-center gap-3 p-2 rounded-lg hover:bg-muted-teal/10 transition-colors text-left"
              >
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <Facebook className="h-4 w-4 text-white" />
                </div>
                <span className="text-slate-gray">Facebook</span>
              </button>

              <button
                onClick={() => openShareWindow(shareLinks.twitter)}
                className="w-full flex items-center gap-3 p-2 rounded-lg hover:bg-muted-teal/10 transition-colors text-left"
              >
                <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center">
                  <Twitter className="h-4 w-4 text-white" />
                </div>
                <span className="text-slate-gray">Twitter</span>
              </button>

              <button
                onClick={() => openShareWindow(shareLinks.linkedin)}
                className="w-full flex items-center gap-3 p-2 rounded-lg hover:bg-muted-teal/10 transition-colors text-left"
              >
                <div className="w-8 h-8 bg-blue-700 rounded-full flex items-center justify-center">
                  <Linkedin className="h-4 w-4 text-white" />
                </div>
                <span className="text-slate-gray">LinkedIn</span>
              </button>

              <button
                onClick={copyToClipboard}
                className="w-full flex items-center gap-3 p-2 rounded-lg hover:bg-muted-teal/10 transition-colors text-left"
              >
                <div className="w-8 h-8 bg-warm-sand rounded-full flex items-center justify-center">
                  {copied ? (
                    <Check className="h-4 w-4 text-slate-gray" />
                  ) : (
                    <Link className="h-4 w-4 text-slate-gray" />
                  )}
                </div>
                <span className="text-slate-gray">{copied ? "Copied!" : "Copy Link"}</span>
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
