"use client"

import { useState, useEffect } from "react"
import { Skeleton } from "@/components/ui/skeleton"

interface ImageSkeletonProps {
  src: string
  alt: string
  className?: string
  aspectRatio?: "square" | "video" | "portrait"
  showSkeletonFor?: number
}

export function ImageSkeleton({
  src,
  alt,
  className = "",
  aspectRatio = "square",
  showSkeletonFor = 2000,
}: ImageSkeletonProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [showImage, setShowImage] = useState(false)

  useEffect(() => {
    // Show skeleton for specified duration
    const timer = setTimeout(() => {
      setIsLoading(false)
      setShowImage(true)
    }, showSkeletonFor)

    return () => clearTimeout(timer)
  }, [showSkeletonFor])

  const aspectRatioClass = {
    square: "aspect-square",
    video: "aspect-video",
    portrait: "aspect-[3/4]",
  }[aspectRatio]

  if (isLoading) {
    return (
      <div className={`${aspectRatioClass} ${className}`}>
        <Skeleton className="w-full h-full rounded-xl" />
      </div>
    )
  }

  return (
    <div className={`${aspectRatioClass} ${className} overflow-hidden rounded-xl`}>
      <img
        src={src || "/placeholder.svg"}
        alt={alt}
        className="w-full h-full object-cover transition-opacity duration-300"
        style={{ opacity: showImage ? 1 : 0 }}
      />
    </div>
  )
}
