"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Loader2 } from "lucide-react"

interface LoadMoreButtonProps {
  onLoadMore: () => void
  isLoading?: boolean
  hasMore?: boolean
  className?: string
}

export function LoadMoreButton({ onLoadMore, isLoading = false, hasMore = true, className = "" }: LoadMoreButtonProps) {
  const handleClick = () => {
    if (!isLoading && hasMore) {
      onLoadMore()
    }
  }

  if (!hasMore) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No more content to load</p>
      </div>
    )
  }

  return (
    <div className={`flex justify-center py-8 ${className}`}>
      <Button
        onClick={handleClick}
        disabled={isLoading}
        variant="outline"
        className="px-8 py-3 rounded-xl border-2 hover:bg-secondary/50 transition-all duration-200 bg-transparent"
      >
        {isLoading ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Loading...
          </>
        ) : (
          "Load More"
        )}
      </Button>
    </div>
  )
}
