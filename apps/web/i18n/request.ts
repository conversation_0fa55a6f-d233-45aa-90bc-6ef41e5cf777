import { notFound } from "next/navigation"
import { getRequestConfig } from "next-intl/server"

const locales = ["et", "en"] as const

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!locale || !locales.includes(locale as any)) {
    notFound()
  }

  // TypeScript assertion to ensure locale is defined after validation
  const validLocale = locale as string

  return {
    locale: validLocale,
    messages: (await import(`../messages/${validLocale}.json`)).default,
  }
})
