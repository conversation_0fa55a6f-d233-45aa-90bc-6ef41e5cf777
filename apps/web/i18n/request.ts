import { getRequestConfig } from "next-intl/server"

const locales = ["et", "en"] as const

export default getRequestConfig(async ({ locale }) => {
  // Handle the case where locale might be undefined (for root routes)
  // Default to Estonian for root routes, validate English for /en routes
  const validLocale = locale || 'et'

  // Only validate if locale is provided and ensure it's valid
  if (locale && !locales.includes(locale as any)) {
    // For invalid locales, default to Estonian
    const fallbackLocale = 'et'
    return {
      locale: fallbackLocale,
      messages: (await import(`../messages/${fallbackLocale}.json`)).default,
    }
  }

  return {
    locale: validLocale,
    messages: (await import(`../messages/${validLocale}.json`)).default,
  }
})
