import { getRequestConfig } from "next-intl/server"

const locales = ["et", "en"] as const

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!locale || !locales.includes(locale as any)) {
    // Default to Estonian for invalid/missing locales
    const fallbackLocale = 'et'
    return {
      locale: fallbackLocale,
      messages: (await import(`../messages/${fallbackLocale}.json`)).default,
    }
  }

  return {
    locale,
    messages: (await import(`../messages/${locale}.json`)).default,
  }
})
