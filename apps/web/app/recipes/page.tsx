import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, Users, ChefHat, Search, Bookmark, Star } from "lucide-react"
import { Input } from "@/components/ui/input"
import { SocialShare } from "@/components/social-share"
import { RecipeRating } from "@/components/recipe-rating"

const recipes = [
  {
    id: 1,
    name: "Golden Turmeric Latte",
    description: "A warming, anti-inflammatory drink perfect for cozy mornings and mindful moments.",
    category: "Beverages",
    difficulty: "Easy",
    prepTime: "5 min",
    cookTime: "5 min",
    servings: 2,
    image: "/placeholder.svg?height=300&width=400",
    tags: ["Vegan", "Gluten-Free", "Anti-inflammatory"],
    rating: 4.8,
    reviewCount: 24,
  },
  {
    id: 2,
    name: "Nourishing Buddha Bowl",
    description: "A colorful, nutrient-packed bowl that celebrates seasonal vegetables and wholesome grains.",
    category: "Main Course",
    difficulty: "Medium",
    prepTime: "20 min",
    cookTime: "25 min",
    servings: 4,
    image: "/placeholder.svg?height=300&width=400",
    tags: ["Vegetarian", "Healthy", "Meal Prep"],
    rating: 4.6,
    reviewCount: 18,
  },
  {
    id: 3,
    name: "Lavender Honey Cookies",
    description: "Delicate, floral cookies that bring a touch of elegance to afternoon tea time.",
    category: "Desserts",
    difficulty: "Medium",
    prepTime: "15 min",
    cookTime: "12 min",
    servings: 24,
    image: "/placeholder.svg?height=300&width=400",
    tags: ["Vegetarian", "Floral", "Tea Time"],
    rating: 4.9,
    reviewCount: 31,
  },
  {
    id: 4,
    name: "Seasonal Vegetable Soup",
    description: "A comforting, adaptable soup recipe that celebrates whatever vegetables are in season.",
    category: "Soups",
    difficulty: "Easy",
    prepTime: "15 min",
    cookTime: "30 min",
    servings: 6,
    image: "/placeholder.svg?height=300&width=400",
    tags: ["Vegan", "Seasonal", "Comfort Food"],
    rating: 4.7,
    reviewCount: 22,
  },
  {
    id: 5,
    name: "Overnight Chia Pudding",
    description: "A nutritious, make-ahead breakfast that's as beautiful as it is delicious.",
    category: "Breakfast",
    difficulty: "Easy",
    prepTime: "10 min",
    cookTime: "0 min",
    servings: 2,
    image: "/placeholder.svg?height=300&width=400",
    tags: ["Vegan", "Make-Ahead", "Superfood"],
    rating: 4.5,
    reviewCount: 15,
  },
  {
    id: 6,
    name: "Herb-Crusted Salmon",
    description: "Fresh herbs and simple seasonings let the natural flavors of the fish shine through.",
    category: "Main Course",
    difficulty: "Medium",
    prepTime: "10 min",
    cookTime: "15 min",
    servings: 4,
    image: "/placeholder.svg?height=300&width=400",
    tags: ["Healthy", "Omega-3", "Quick"],
    rating: 4.4,
    reviewCount: 12,
  },
]

const categories = ["All", "Breakfast", "Main Course", "Soups", "Desserts", "Beverages"]
const difficulties = ["All", "Easy", "Medium", "Advanced"]

// Sample reviews data for the first recipe
const sampleReviews = [
  {
    id: 1,
    userName: "Sarah M.",
    rating: 5,
    comment:
      "This turmeric latte has become my morning ritual! The perfect balance of spices and so comforting. I love how it makes me feel energized yet calm.",
    date: "2 days ago",
    helpful: 8,
    verified: true,
  },
  {
    id: 2,
    userName: "Maria K.",
    rating: 4,
    comment:
      "Really delicious! I added a bit more honey to suit my taste. The anti-inflammatory benefits are a bonus. Will definitely make again.",
    date: "1 week ago",
    helpful: 5,
    verified: false,
  },
  {
    id: 3,
    userName: "Emma L.",
    rating: 5,
    comment: "Perfect recipe! Easy to follow and the result is restaurant-quality. My whole family loves it now.",
    date: "2 weeks ago",
    helpful: 12,
    verified: true,
  },
]

export default function RecipesPage() {
  return (
    <div className="min-h-screen bg-background pt-20">
      {/* Header */}
      <section className="bg-gradient-to-br from-background to-secondary/20 py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl sm:text-5xl font-bold text-foreground mb-6">Nourishing Recipes</h1>
            <p className="text-lg text-muted-foreground mb-8">
              Simple, wholesome recipes that celebrate seasonal ingredients and bring joy to your kitchen and table.
            </p>

            {/* Search */}
            <div className="relative w-full sm:w-96 mx-auto mb-6">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search recipes..."
                className="pl-10 rounded-xl border-2 border-secondary/50 focus:border-primary"
              />
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex gap-2 flex-wrap justify-center">
                <span className="text-sm text-muted-foreground self-center">Category:</span>
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={category === "All" ? "default" : "outline"}
                    size="sm"
                    className="rounded-full"
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>

            <div className="flex gap-2 flex-wrap justify-center mt-4">
              <span className="text-sm text-muted-foreground self-center">Difficulty:</span>
              {difficulties.map((difficulty) => (
                <Button
                  key={difficulty}
                  variant={difficulty === "All" ? "default" : "outline"}
                  size="sm"
                  className="rounded-full"
                >
                  {difficulty}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Recipes Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {recipes.map((recipe) => (
              <Card
                key={recipe.id}
                className="group hover:shadow-soft-hover transition-all duration-300 border-0 shadow-soft rounded-2xl overflow-hidden"
              >
                <div className="relative overflow-hidden">
                  <img
                    src={recipe.image || "/placeholder.svg"}
                    alt={recipe.name}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <Badge className="absolute top-4 left-4 bg-primary text-primary-foreground">{recipe.category}</Badge>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="absolute top-4 right-4 bg-white/80 hover:bg-white text-foreground rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  >
                    <Bookmark className="h-4 w-4" />
                  </Button>
                  <Badge
                    variant="outline"
                    className={`absolute bottom-4 left-4 bg-white/90 ${
                      recipe.difficulty === "Easy"
                        ? "text-green-600 border-green-200"
                        : recipe.difficulty === "Medium"
                          ? "text-yellow-600 border-yellow-200"
                          : "text-red-600 border-red-200"
                    }`}
                  >
                    {recipe.difficulty}
                  </Badge>
                </div>
                <CardHeader className="pb-3">
                  <CardTitle className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors duration-300">
                    {recipe.name}
                  </CardTitle>
                  <p className="text-muted-foreground text-sm line-clamp-2">{recipe.description}</p>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="flex items-center">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-4 h-4 ${
                            star <= Math.floor(recipe.rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm font-medium text-slate-gray">{recipe.rating}</span>
                    <span className="text-sm text-slate-gray/70">({recipe.reviewCount} reviews)</span>
                  </div>

                  <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{recipe.prepTime}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <ChefHat className="h-4 w-4" />
                        <span>{recipe.cookTime}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        <span>{recipe.servings}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-1 mb-4">
                    {recipe.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center gap-2">
                    <Button className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl group-hover:shadow-soft transition-all duration-300">
                      View Recipe
                    </Button>
                    <SocialShare title={recipe.name} description={recipe.description} url={`/recipes/${recipe.id}`} />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="mt-16">
            <h2 className="text-2xl font-bold text-slate-gray mb-8 text-center">Recipe Reviews Example</h2>
            <div className="max-w-4xl mx-auto">
              <RecipeRating
                recipeId={1}
                recipeName="Golden Turmeric Latte"
                averageRating={4.8}
                totalReviews={24}
                reviews={sampleReviews}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Recipe of the Week */}
      <section className="py-16 bg-secondary/20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">Recipe of the Week</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Each week, I share a special recipe that's close to my heart - often inspired by the season, a memory, or
              a new discovery.
            </p>
          </div>

          <div className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-3xl p-8 md:p-12">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <Badge className="bg-accent text-white mb-4">Featured Recipe</Badge>
                <h3 className="text-2xl font-bold text-foreground mb-4">Grandmother's Apple Cinnamon Bread</h3>
                <p className="text-muted-foreground mb-6">
                  A cherished family recipe that fills your home with the most wonderful aroma and your heart with warm
                  memories. Perfect for autumn mornings and cozy afternoons.
                </p>
                <div className="flex items-center gap-6 text-sm text-muted-foreground mb-6">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>20 min prep</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <ChefHat className="h-4 w-4" />
                    <span>55 min bake</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>Serves 8</span>
                  </div>
                </div>
                <Button className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl">
                  Get the Recipe
                </Button>
              </div>
              <div className="relative">
                <img
                  src="/placeholder.svg?height=300&width=400"
                  alt="Apple Cinnamon Bread"
                  className="w-full h-auto rounded-2xl shadow-soft"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
