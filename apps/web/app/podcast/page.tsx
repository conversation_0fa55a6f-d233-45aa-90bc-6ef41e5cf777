import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Play, Clock, Calendar, Download, Share2, Heart } from "lucide-react"
import { Input } from "@/components/ui/input"

const episodes = [
  {
    id: 1,
    title: "Finding Your Creative Voice",
    description:
      "A heartfelt conversation about discovering what makes you unique and how to express it authentically in your work and life.",
    duration: "42:15",
    date: "2024-01-15",
    category: "Creativity",
    plays: 1247,
    image: "/placeholder.svg?height=200&width=200",
    featured: true,
  },
  {
    id: 2,
    title: "The Art of Slow Mornings",
    description:
      "Exploring how intentional morning routines can transform your entire day and create space for what matters most.",
    duration: "28:33",
    date: "2024-01-08",
    category: "Lifestyle",
    plays: 892,
    image: "/placeholder.svg?height=200&width=200",
    featured: false,
  },
  {
    id: 3,
    title: "Seasonal Living with <PERSON>",
    description:
      "Guest <PERSON> shares how aligning our lives with nature's rhythms can bring more balance and joy to our everyday experiences.",
    duration: "51:22",
    date: "2024-01-01",
    category: "Wellness",
    plays: 1156,
    image: "/placeholder.svg?height=200&width=200",
    featured: false,
  },
  {
    id: 4,
    title: "Creating Sacred Spaces at Home",
    description:
      "Simple ways to transform any corner of your home into a peaceful retreat that nurtures your soul and creativity.",
    duration: "35:18",
    date: "2023-12-25",
    category: "Home",
    plays: 743,
    image: "/placeholder.svg?height=200&width=200",
    featured: false,
  },
  {
    id: 5,
    title: "The Power of Gratitude Practice",
    description:
      "Why gratitude isn't just a buzzword and how a simple daily practice can shift your perspective on everything.",
    duration: "31:45",
    date: "2023-12-18",
    category: "Mindfulness",
    plays: 1089,
    image: "/placeholder.svg?height=200&width=200",
    featured: false,
  },
  {
    id: 6,
    title: "Building Community in Digital Spaces",
    description: "How to create genuine connections online while maintaining authenticity and protecting your energy.",
    duration: "39:12",
    date: "2023-12-11",
    category: "Community",
    plays: 654,
    image: "/placeholder.svg?height=200&width=200",
    featured: false,
  },
]

const categories = ["All", "Creativity", "Lifestyle", "Wellness", "Home", "Mindfulness", "Community"]

export default function PodcastPage() {
  return (
    <div className="min-h-screen bg-background pt-20">
      {/* Header */}
      <section className="bg-gradient-to-br from-background to-secondary/20 py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl sm:text-5xl font-bold text-foreground mb-6">Conversations & Reflections</h1>
            <p className="text-lg text-muted-foreground mb-8">
              Join me for intimate conversations about creativity, mindful living, and finding beauty in the everyday.
              New episodes every Monday.
            </p>

            {/* Podcast Stats */}
            <div className="flex flex-wrap justify-center gap-8 mb-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">24</div>
                <div className="text-sm text-muted-foreground">Episodes</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">12K+</div>
                <div className="text-sm text-muted-foreground">Downloads</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">4.9</div>
                <div className="text-sm text-muted-foreground">Rating</div>
              </div>
            </div>

            {/* Subscribe Buttons */}
            <div className="flex flex-wrap justify-center gap-4">
              <Button className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl">
                Subscribe on Apple Podcasts
              </Button>
              <Button variant="outline" className="rounded-xl bg-transparent">
                Subscribe on Spotify
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Episode */}
      {episodes
        .filter((ep) => ep.featured)
        .map((episode) => (
          <section key={episode.id} className="py-16 bg-secondary/10">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-8">
                <Badge className="bg-accent text-white mb-4">Latest Episode</Badge>
                <h2 className="text-3xl font-bold text-foreground mb-4">Featured</h2>
              </div>

              <Card className="max-w-4xl mx-auto border-0 shadow-soft-hover rounded-3xl overflow-hidden">
                <div className="grid md:grid-cols-2 gap-0">
                  <div className="relative">
                    <img
                      src={episode.image || "/placeholder.svg"}
                      alt={episode.title}
                      className="w-full h-64 md:h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    <Button
                      size="lg"
                      className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-foreground rounded-full w-16 h-16"
                    >
                      <Play className="h-6 w-6 ml-1" />
                    </Button>
                  </div>
                  <div className="p-8">
                    <Badge className="bg-primary text-primary-foreground mb-4">{episode.category}</Badge>
                    <h3 className="text-2xl font-bold text-foreground mb-4">{episode.title}</h3>
                    <p className="text-muted-foreground mb-6">{episode.description}</p>

                    <div className="flex items-center gap-6 text-sm text-muted-foreground mb-6">
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{episode.duration}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(episode.date).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Play className="h-4 w-4" />
                        <span>{episode.plays} plays</span>
                      </div>
                    </div>

                    <div className="flex gap-3">
                      <Button className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl flex-1">
                        <Play className="h-4 w-4 mr-2" />
                        Listen Now
                      </Button>
                      <Button variant="outline" size="icon" className="rounded-xl bg-transparent">
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="icon" className="rounded-xl bg-transparent">
                        <Share2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </section>
        ))}

      {/* Episodes List */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">All Episodes</h2>

            {/* Categories Filter */}
            <div className="flex gap-2 flex-wrap justify-center">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={category === "All" ? "default" : "outline"}
                  size="sm"
                  className="rounded-full"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            {episodes
              .filter((ep) => !ep.featured)
              .map((episode) => (
                <Card
                  key={episode.id}
                  className="group hover:shadow-soft-hover transition-all duration-300 border-0 shadow-soft rounded-2xl overflow-hidden"
                >
                  <div className="grid md:grid-cols-4 gap-0">
                    <div className="relative">
                      <img
                        src={episode.image || "/placeholder.svg"}
                        alt={episode.title}
                        className="w-full h-32 md:h-full object-cover"
                      />
                      <Button
                        size="sm"
                        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-foreground rounded-full w-10 h-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      >
                        <Play className="h-4 w-4 ml-0.5" />
                      </Button>
                    </div>
                    <div className="md:col-span-3 p-6">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <Badge variant="outline" className="text-xs">
                              {episode.category}
                            </Badge>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>{episode.duration}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>{new Date(episode.date).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                          <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors duration-300 mb-2">
                            {episode.title}
                          </h3>
                          <p className="text-muted-foreground text-sm line-clamp-2 mb-4">{episode.description}</p>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Play className="h-3 w-3" />
                          <span>{episode.plays} plays</span>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg"
                          >
                            Listen
                          </Button>
                          <Button variant="outline" size="sm" className="rounded-lg bg-transparent">
                            <Download className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm" className="rounded-lg bg-transparent">
                            <Heart className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-16 bg-secondary/20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-foreground mb-4">Never Miss an Episode</h2>
            <p className="text-muted-foreground mb-8">
              Get notified when new episodes are released, plus exclusive behind-the-scenes content and early access to
              special episodes.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                placeholder="Your email address"
                className="rounded-xl border-2 border-secondary/50 focus:border-primary"
              />
              <Button className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl px-8">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
