import type { <PERSON>ada<PERSON> } from "next"
import { Inter, Poppins } from "next/font/google"
import "./globals.css"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { ErrorBoundary } from "@/components/error-boundary"
import { ToastContainer } from "@/components/error-toast"
import { RouteLoader } from "@/components/route-loader"
// Temporarily disable next-intl for testing
// import { NextIntlClientProvider } from "next-intl"
// import { getMessages } from "next-intl/server"

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
})

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
  variable: "--font-poppins",
})

export const metadata: Metadata = {
  title: "Time With Tuuli - Blog, Store, Recipes & More",
  description:
    "Join <PERSON>'s creative world - discover inspiring content, shop unique products, try delicious recipes, and connect with our community.",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="et" className={`${inter.variable} ${poppins.variable} antialiased`}>
      <body className="font-sans">
        <ErrorBoundary>
          <RouteLoader />
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-1">{children}</main>
            <Footer />
          </div>
          <ToastContainer />
        </ErrorBoundary>
      </body>
    </html>
  )
}
