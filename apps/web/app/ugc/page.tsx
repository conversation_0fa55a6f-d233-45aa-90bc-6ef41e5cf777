"use client"

import { useState } from "react"
import { Heart, MessageCircle, Share, Bookmark, MoreHorizontal, Play, ImageIcon, FileText, Video } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ImageSkeleton } from "@/components/image-skeleton"

export default function UGCPage() {
  const [activeTab, setActiveTab] = useState("all")

  const posts = [
    {
      id: 1,
      type: "picture",
      user: { name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40", username: "emma_creates" },
      image: "/placeholder.svg?height=400&width=400",
      caption: "Finally organized my creative corner! ✨ Thanks to @timewithtuuli for the inspiration 💕",
      likes: 234,
      comments: 18,
      timeAgo: "2h",
    },
    {
      id: 2,
      type: "copy",
      user: { name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40", username: "sarahs_journey" },
      content:
        "Just wanted to share how <PERSON><PERSON>'s morning routine has completely transformed my days! 🌅 I used to struggle with getting up early, but following her gentle approach to building habits has made such a difference. The key was starting small - just 5 minutes of journaling and a cup of tea by the window. Now it's become this beautiful ritual that sets the tone for everything else. Thank you for showing us that self-care doesn't have to be complicated! #TimeWithTuuli #MorningRituals #MindfulLiving",
      likes: 189,
      comments: 12,
      timeAgo: "4h",
    },
    {
      id: 3,
      type: "video",
      user: { name: "Alex R.", avatar: "/placeholder.svg?height=40&width=40", username: "alex_mindful" },
      thumbnail: "/placeholder.svg?height=400&width=400",
      duration: "2:34",
      caption: "Created my meditation space following the mindful living tips. Game changer! 🧘‍♂️",
      likes: 156,
      comments: 24,
      timeAgo: "6h",
    },
    {
      id: 4,
      type: "picture",
      user: { name: "Lisa T.", avatar: "/placeholder.svg?height=40&width=40", username: "lisa_creates" },
      image: "/placeholder.svg?height=400&width=400",
      caption: "My first pottery class! Inspired by the creative living podcast episode 🏺",
      likes: 298,
      comments: 31,
      timeAgo: "8h",
    },
    {
      id: 5,
      type: "copy",
      user: { name: "Mike D.", avatar: "/placeholder.svg?height=40&width=40", username: "mike_wellness" },
      content:
        "Halfway through the 30-day mindful movement challenge and I'm feeling incredible! 💪 What started as just 10 minutes of stretching has evolved into this whole new relationship with my body. Tuuli's approach to fitness being about feeling good rather than looking a certain way really resonated with me. I'm stronger, more flexible, and honestly just happier. To anyone on the fence about starting - just begin where you are! #TimeWithTuuli #MindfulMovement #WellnessJourney",
      likes: 167,
      comments: 15,
      timeAgo: "12h",
    },
    {
      id: 6,
      type: "video",
      user: { name: "Anna L.", avatar: "/placeholder.svg?height=40&width=40", username: "anna_lifestyle" },
      thumbnail: "/placeholder.svg?height=400&width=400",
      duration: "1:45",
      caption: "My little garden paradise 🌸 Following all the sustainable living tips!",
      likes: 445,
      comments: 52,
      timeAgo: "1d",
    },
  ]

  const filteredPosts = activeTab === "all" ? posts : posts.filter((post) => post.type === activeTab)

  const renderPost = (post: any) => (
    <div key={post.id} className="bg-card rounded-2xl shadow-soft overflow-hidden">
      {/* Post Header */}
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-3">
          <Avatar className="w-10 h-10">
            <AvatarImage src={post.user.avatar || "/placeholder.svg"} alt={post.user.name} />
            <AvatarFallback>{post.user.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div>
            <p className="font-semibold text-foreground text-sm">{post.user.username}</p>
            <p className="text-muted-foreground text-xs">{post.timeAgo}</p>
          </div>
        </div>
        <Button variant="ghost" size="sm" className="p-2">
          <MoreHorizontal className="w-4 h-4" />
        </Button>
      </div>

      {/* Post Content */}
      {post.type === "picture" && (
        <ImageSkeleton
          src={post.image || "/placeholder.svg"}
          alt="User post"
          aspectRatio="square"
          showSkeletonFor={2000}
        />
      )}

      {post.type === "video" && (
        <div className="aspect-square relative bg-black">
          <ImageSkeleton
            src={post.thumbnail || "/placeholder.svg"}
            alt="Video thumbnail"
            aspectRatio="square"
            showSkeletonFor={2000}
            className="absolute inset-0"
          />
          <div className="absolute inset-0 flex items-center justify-center z-10">
            <div className="bg-black/50 rounded-full p-4">
              <Play className="w-8 h-8 text-white fill-white" />
            </div>
          </div>
          <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded z-10">
            {post.duration}
          </div>
        </div>
      )}

      {post.type === "copy" && (
        <div className="p-4 bg-secondary/20">
          <p className="text-foreground leading-relaxed">{post.content}</p>
        </div>
      )}

      {/* Post Actions */}
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" className="p-0 hover:bg-transparent">
              <Heart className="w-6 h-6 text-foreground hover:text-accent transition-colors" />
            </Button>
            <Button variant="ghost" size="sm" className="p-0 hover:bg-transparent">
              <MessageCircle className="w-6 h-6 text-foreground hover:text-primary transition-colors" />
            </Button>
            <Button variant="ghost" size="sm" className="p-0 hover:bg-transparent">
              <Share className="w-6 h-6 text-foreground hover:text-primary transition-colors" />
            </Button>
          </div>
          <Button variant="ghost" size="sm" className="p-0 hover:bg-transparent">
            <Bookmark className="w-6 h-6 text-foreground hover:text-primary transition-colors" />
          </Button>
        </div>

        {/* Likes */}
        <p className="font-semibold text-foreground text-sm mb-2">{post.likes.toLocaleString()} likes</p>

        {/* Caption for picture/video posts */}
        {(post.type === "picture" || post.type === "video") && post.caption && (
          <div className="text-sm">
            <span className="font-semibold text-foreground mr-2">{post.user.username}</span>
            <span className="text-foreground">{post.caption}</span>
          </div>
        )}

        {/* Comments */}
        {post.comments > 0 && (
          <button className="text-muted-foreground text-sm mt-2 hover:text-foreground transition-colors">
            View all {post.comments} comments
          </button>
        )}
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-background pt-20">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="font-heading text-4xl font-bold text-foreground mb-4">Community Stories</h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            See how our community is living creatively and mindfully. Share your journey with #TimeWithTuuli
          </p>
        </div>

        {/* Content Type Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
          <TabsList className="grid w-full grid-cols-4 bg-secondary/30 rounded-xl p-1">
            <TabsTrigger
              value="all"
              className="rounded-lg data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              All
            </TabsTrigger>
            <TabsTrigger
              value="picture"
              className="rounded-lg data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              <ImageIcon className="w-4 h-4 mr-2" />
              Pictures
            </TabsTrigger>
            <TabsTrigger
              value="video"
              className="rounded-lg data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              <Video className="w-4 h-4 mr-2" />
              Videos
            </TabsTrigger>
            <TabsTrigger
              value="copy"
              className="rounded-lg data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              <FileText className="w-4 h-4 mr-2" />
              Stories
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-8">
            {/* Posts Feed */}
            <div className="space-y-8">{filteredPosts.map(renderPost)}</div>
          </TabsContent>
        </Tabs>

        {/* Call to Action */}
        <div className="text-center mt-12 p-8 bg-secondary/30 rounded-2xl">
          <h3 className="font-heading text-2xl font-bold text-foreground mb-4">Share Your Story</h3>
          <p className="text-muted-foreground mb-6">Tag us @timewithtuuli and use #TimeWithTuuli to be featured!</p>
          <Button className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl px-8">
            Join Our Community
          </Button>
        </div>
      </div>
    </div>
  )
}
