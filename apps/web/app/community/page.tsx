import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Heart, MessageCircle, Share2, Users, Calendar, MapPin, ExternalLink } from "lucide-react"
import { Textarea } from "@/components/ui/textarea"

const communityPosts = [
  {
    id: 1,
    author: "<PERSON>",
    avatar: "/placeholder.svg?height=40&width=40",
    content:
      "Just tried <PERSON><PERSON>'s golden turmeric latte recipe and it's absolutely divine! The perfect way to start a cozy Sunday morning. Thank you for sharing such beautiful, nourishing recipes.",
    image: "/placeholder.svg?height=200&width=300",
    likes: 24,
    comments: 8,
    timeAgo: "2 hours ago",
    category: "Recipe Share",
  },
  {
    id: 2,
    author: "<PERSON>",
    avatar: "/placeholder.svg?height=40&width=40",
    content:
      "Your latest podcast episode on finding creative voice really resonated with me. I've been struggling with imposter syndrome in my art practice, and your words gave me the courage to keep going.",
    likes: 31,
    comments: 12,
    timeAgo: "5 hours ago",
    category: "Inspiration",
  },
  {
    id: 3,
    author: "Lisa R.",
    avatar: "/placeholder.svg?height=40&width=40",
    content:
      "Created my own little reading nook inspired by your home sanctuary post! It's become my favorite spot for morning coffee and journaling. Sometimes the smallest changes make the biggest difference.",
    image: "/placeholder.svg?height=200&width=300",
    likes: 18,
    comments: 6,
    timeAgo: "1 day ago",
    category: "Home & Space",
  },
  {
    id: 4,
    author: "Maria L.",
    avatar: "/placeholder.svg?height=40&width=40",
    content:
      "Thank you for creating such a warm, authentic space online. In a world of highlight reels, your genuine approach to sharing life's ups and downs is so refreshing and needed.",
    likes: 42,
    comments: 15,
    timeAgo: "2 days ago",
    category: "Gratitude",
  },
]

const upcomingEvents = [
  {
    id: 1,
    title: "Virtual Tea & Talk Session",
    date: "January 28, 2024",
    time: "2:00 PM EST",
    description: "Join me for an intimate conversation about seasonal living and setting intentions for the new year.",
    attendees: 24,
    maxAttendees: 30,
    type: "Virtual",
  },
  {
    id: 2,
    title: "Mindful Morning Workshop",
    date: "February 10, 2024",
    time: "9:00 AM EST",
    description: "A guided session on creating morning rituals that nourish your soul and set the tone for your day.",
    attendees: 18,
    maxAttendees: 25,
    type: "Virtual",
  },
  {
    id: 3,
    title: "Community Recipe Exchange",
    date: "February 18, 2024",
    time: "3:00 PM EST",
    description: "Share your favorite nourishing recipes and discover new ones from our beautiful community.",
    attendees: 12,
    maxAttendees: 20,
    type: "Virtual",
  },
]

export default function CommunityPage() {
  return (
    <div className="min-h-screen bg-background pt-20">
      {/* Header */}
      <section className="bg-gradient-to-br from-background to-secondary/20 py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl sm:text-5xl font-bold text-foreground mb-6">Our Beautiful Community</h1>
            <p className="text-lg text-muted-foreground mb-8">
              A space for kindred spirits to connect, share inspiration, and support each other on the journey of
              mindful, creative living.
            </p>

            {/* Community Stats */}
            <div className="flex flex-wrap justify-center gap-8 mb-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">2.4K+</div>
                <div className="text-sm text-muted-foreground">Community Members</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">156</div>
                <div className="text-sm text-muted-foreground">Stories Shared</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">48</div>
                <div className="text-sm text-muted-foreground">Events Hosted</div>
              </div>
            </div>

            <Button className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl px-8">
              Join Our Community
            </Button>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Share Something */}
            <Card className="border-0 shadow-soft rounded-2xl">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-foreground">Share with the Community</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  placeholder="What's inspiring you today? Share a recipe, a moment of gratitude, or something beautiful you've discovered..."
                  className="min-h-[100px] rounded-xl border-2 border-secondary/50 focus:border-primary resize-none"
                />
                <div className="flex items-center justify-between">
                  <div className="flex gap-2">
                    <Badge
                      variant="outline"
                      className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                    >
                      Recipe Share
                    </Badge>
                    <Badge
                      variant="outline"
                      className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                    >
                      Inspiration
                    </Badge>
                    <Badge
                      variant="outline"
                      className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                    >
                      Gratitude
                    </Badge>
                  </div>
                  <Button className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl">Share</Button>
                </div>
              </CardContent>
            </Card>

            {/* Community Posts */}
            <div className="space-y-6">
              {communityPosts.map((post) => (
                <Card
                  key={post.id}
                  className="border-0 shadow-soft rounded-2xl hover:shadow-soft-hover transition-shadow duration-300"
                >
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <Avatar className="w-12 h-12">
                        <AvatarImage src={post.avatar || "/placeholder.svg"} alt={post.author} />
                        <AvatarFallback>
                          {post.author
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="font-semibold text-foreground">{post.author}</h4>
                          <Badge variant="outline" className="text-xs">
                            {post.category}
                          </Badge>
                          <span className="text-sm text-muted-foreground">{post.timeAgo}</span>
                        </div>
                        <p className="text-muted-foreground mb-4 leading-relaxed">{post.content}</p>

                        {post.image && (
                          <div className="mb-4">
                            <img
                              src={post.image || "/placeholder.svg"}
                              alt="Community post"
                              className="w-full max-w-md h-48 object-cover rounded-xl"
                            />
                          </div>
                        )}

                        <div className="flex items-center gap-6 text-sm text-muted-foreground">
                          <button className="flex items-center gap-2 hover:text-accent transition-colors">
                            <Heart className="h-4 w-4" />
                            <span>{post.likes}</span>
                          </button>
                          <button className="flex items-center gap-2 hover:text-primary transition-colors">
                            <MessageCircle className="h-4 w-4" />
                            <span>{post.comments}</span>
                          </button>
                          <button className="flex items-center gap-2 hover:text-primary transition-colors">
                            <Share2 className="h-4 w-4" />
                            <span>Share</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Upcoming Events */}
            <Card className="border-0 shadow-soft rounded-2xl">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-foreground flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-primary" />
                  Upcoming Events
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="p-4 bg-secondary/10 rounded-xl">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-semibold text-foreground text-sm">{event.title}</h4>
                      <Badge variant="outline" className="text-xs">
                        {event.type}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3 leading-relaxed">{event.description}</p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground mb-3">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>{event.date}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        <span>{event.time}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Users className="h-3 w-3" />
                        <span>
                          {event.attendees}/{event.maxAttendees} attending
                        </span>
                      </div>
                      <Button
                        size="sm"
                        className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg text-xs"
                      >
                        Join Event
                      </Button>
                    </div>
                  </div>
                ))}
                <Button variant="outline" className="w-full rounded-xl bg-transparent">
                  View All Events
                </Button>
              </CardContent>
            </Card>

            {/* Community Guidelines */}
            <Card className="border-0 shadow-soft rounded-2xl">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-foreground">Community Guidelines</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm text-muted-foreground">
                <div className="flex items-start gap-3">
                  <Heart className="h-4 w-4 text-accent mt-0.5 flex-shrink-0" />
                  <span>Share with kindness and authenticity</span>
                </div>
                <div className="flex items-start gap-3">
                  <Users className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Support and uplift fellow community members</span>
                </div>
                <div className="flex items-start gap-3">
                  <MessageCircle className="h-4 w-4 text-secondary mt-0.5 flex-shrink-0" />
                  <span>Keep conversations respectful and constructive</span>
                </div>
                <div className="flex items-start gap-3">
                  <Share2 className="h-4 w-4 text-accent mt-0.5 flex-shrink-0" />
                  <span>Credit sources when sharing others' content</span>
                </div>
              </CardContent>
            </Card>

            {/* Connect */}
            <Card className="border-0 shadow-soft rounded-2xl">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-foreground">Connect Beyond</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start rounded-xl bg-transparent">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Join Discord Community
                </Button>
                <Button variant="outline" className="w-full justify-start rounded-xl bg-transparent">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Follow on Instagram
                </Button>
                <Button variant="outline" className="w-full justify-start rounded-xl bg-transparent">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Subscribe to Newsletter
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
