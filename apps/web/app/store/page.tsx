import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, Heart, ShoppingCart, Filter, Search } from "lucide-react"
import { Input } from "@/components/ui/input"

const products = [
  {
    id: 1,
    name: "Mindful Morning Journal",
    price: 28,
    originalPrice: 35,
    category: "Stationery",
    rating: 4.9,
    reviews: 127,
    image: "/placeholder.svg?height=300&width=300",
    badge: "Bestseller",
  },
  {
    id: 2,
    name: "Cozy Knit Throw Blanket",
    price: 65,
    category: "Home",
    rating: 4.8,
    reviews: 89,
    image: "/placeholder.svg?height=300&width=300",
    badge: "New",
  },
  {
    id: 3,
    name: "Ceramic Coffee Mug Set",
    price: 42,
    category: "Kitchen",
    rating: 4.7,
    reviews: 156,
    image: "/placeholder.svg?height=300&width=300",
    badge: "",
  },
  {
    id: 4,
    name: "Essential Oil Diffuser",
    price: 58,
    originalPrice: 72,
    category: "Wellness",
    rating: 4.9,
    reviews: 203,
    image: "/placeholder.svg?height=300&width=300",
    badge: "Sale",
  },
  {
    id: 5,
    name: "Handwoven Basket Collection",
    price: 85,
    category: "Home",
    rating: 4.6,
    reviews: 74,
    image: "/placeholder.svg?height=300&width=300",
    badge: "",
  },
  {
    id: 6,
    name: "Organic Tea Blend Set",
    price: 32,
    category: "Wellness",
    rating: 4.8,
    reviews: 142,
    image: "/placeholder.svg?height=300&width=300",
    badge: "Limited",
  },
]

const categories = ["All", "Stationery", "Home", "Kitchen", "Wellness"]

export default function StorePage() {
  return (
    <div className="min-h-screen bg-background pt-20">
      {/* Header */}
      <section className="bg-gradient-to-br from-background to-secondary/20 py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl sm:text-5xl font-bold text-foreground mb-6">Curated Finds</h1>
            <p className="text-lg text-muted-foreground mb-8">
              Thoughtfully selected items that bring beauty, comfort, and mindfulness to your everyday life.
            </p>

            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="relative w-full sm:w-96">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search products..."
                  className="pl-10 rounded-xl border-2 border-secondary/50 focus:border-primary"
                />
              </div>
              <Button variant="outline" className="rounded-xl bg-transparent">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Categories */}
            <div className="flex gap-2 flex-wrap justify-center mt-6">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={category === "All" ? "default" : "outline"}
                  size="sm"
                  className="rounded-full"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {products.map((product) => (
              <Card
                key={product.id}
                className="group hover:shadow-soft-hover transition-all duration-300 border-0 shadow-soft rounded-2xl overflow-hidden"
              >
                <div className="relative overflow-hidden">
                  <img
                    src={product.image || "/placeholder.svg"}
                    alt={product.name}
                    className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  {product.badge && (
                    <Badge
                      className={`absolute top-4 left-4 ${
                        product.badge === "Sale"
                          ? "bg-accent text-white"
                          : product.badge === "New"
                            ? "bg-primary text-primary-foreground"
                            : "bg-secondary text-foreground"
                      }`}
                    >
                      {product.badge}
                    </Badge>
                  )}
                  <Button
                    size="icon"
                    variant="ghost"
                    className="absolute top-4 right-4 bg-white/80 hover:bg-white text-foreground rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  >
                    <Heart className="h-4 w-4" />
                  </Button>
                </div>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs">
                      {product.category}
                    </Badge>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm text-muted-foreground">
                        {product.rating} ({product.reviews})
                      </span>
                    </div>
                  </div>
                  <CardTitle className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors duration-300">
                    {product.name}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <span className="text-2xl font-bold text-foreground">${product.price}</span>
                      {product.originalPrice && (
                        <span className="text-lg text-muted-foreground line-through">${product.originalPrice}</span>
                      )}
                    </div>
                  </div>
                  <Button className="w-full bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl group-hover:shadow-soft transition-all duration-300">
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Add to Cart
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Collection */}
      <section className="py-16 bg-secondary/20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">Featured Collection</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              This month's carefully curated selection of items that embody the essence of mindful living and creative
              expression.
            </p>
          </div>

          <div className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-3xl p-8 md:p-12">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="text-2xl font-bold text-foreground mb-4">Winter Wellness Collection</h3>
                <p className="text-muted-foreground mb-6">
                  Embrace the cozy season with our selection of warming teas, soft textiles, and mindfulness tools
                  designed to nurture your well-being during the colder months.
                </p>
                <Button className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl">
                  Shop Collection
                </Button>
              </div>
              <div className="relative">
                <img
                  src="/placeholder.svg?height=300&width=400"
                  alt="Winter Wellness Collection"
                  className="w-full h-auto rounded-2xl shadow-soft"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
