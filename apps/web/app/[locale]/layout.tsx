import type React from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { ErrorBoundary } from "@/components/error-boundary"
import { ToastContainer } from "@/components/error-toast"
import { RouteLoader } from "@/components/route-loader"
import { NextIntlClientProvider } from "next-intl"
import { getMessages } from "next-intl/server"
import { notFound } from "next/navigation"

const locales = ["en", "et"]

export default async function LocaleLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode
  params: Promise<{ locale: string }>
}>) {
  const { locale } = await params ?? 'et';

  const messages = await getMessages()

  return (
    <NextIntlClientProvider messages={messages}>
      <ErrorBoundary>
        <RouteLoader />
        <div className="min-h-screen flex flex-col">
          <Header />
          <main className="flex-1">{children}</main>
          <Footer />
        </div>
        <ToastContainer />
      </ErrorBoundary>
    </NextIntlClientProvider>
  )
}
