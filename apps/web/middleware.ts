import { NextRequest, NextResponse } from 'next/server'

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname

  // If path starts with /en, keep it as is (English routes)
  if (pathname.startsWith('/en')) {
    return NextResponse.next()
  }

  // If path starts with /et, redirect to remove the /et prefix (Estonian routes)
  if (pathname.startsWith('/et')) {
    const newPath = pathname.replace('/et', '') || '/'
    return NextResponse.redirect(new URL(newPath, request.url))
  }

  // For root paths (/, /blog, etc.), rewrite to /et/path to use the [locale] structure
  // but keep the URL clean without showing /et
  const rewritePath = `/et${pathname === '/' ? '' : pathname}`
  return NextResponse.rewrite(new URL(rewritePath, request.url))
}

export const config = {
  matcher: [
    // Match all paths except static files and API routes
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}